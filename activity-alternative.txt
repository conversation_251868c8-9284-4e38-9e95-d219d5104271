"use client"

import { useState, useEffect, useRef } from "react"
import Link from "next/link"
import { gsap } from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"
import { ScrollToPlugin } from "gsap/ScrollToPlugin"
import {Search,Filter,Calendar,MapPin,Users,ChevronLeft,ChevronRight,BookOpen,Presentation,Globe,Award,Handshake,Quote} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import GSAPReveal from "@/components/gsap-reveal"
import { ActivityGalleryModal } from "@/components/activity-gallery-modal"
import ActivityGalleryHero from "@/components/activity-gallery-hero"
import { TestimonialVideoModal } from "@/components/testimonial-video-modal"

// Mock activities data
const activitiesData = [
  {
    id: 1,
    title: "The Fund organizes a networking event with Palestinian students in Turkey and offers them a number of scholarships.",
    date: "June 15-17, 2023",
    location: "İstanbul, Türkiye",
    description:"The Palestinian Student Support Fund organized a networking event with Palestinian students in Turkey, aiming to better understand their circumstances and needs and to work towards addressing them within its future plans. Scholarships and financial aid were also distributed during the event, reflecting the Fund's commitment to supporting Palestinian students.",
    full_description:"As part of its efforts to strengthen engagement with Palestinian students abroad, the Palestinian Student Support Fund organized a networking event with Palestinian students in Turkey. The event aimed to closely assess their academic and living conditions, listen to their various needs and demands, and work to incorporate these insights into the Fund's future plans. The gathering marked an important milestone in launching a new phase of support and follow-up for Palestinian students in Turkey, through providing psychological, social, and educational assistance, and by strengthening communication channels with them.During the event, a number of scholarships and financial aids were distributed to the attending students, reflecting the Fund's ongoing commitment to supporting the educational journey of Palestinian youth and creating favorable conditions for them to complete their university studies.",
    image: "/one/PHOTO-2025-04-20-18-03-47 5.jpg?height=400&width=600",
    category: "Conference",
    featured: true,
    year: 2023,
  },
  {
    id: 2,
    title: "The Palestinian Student Support Fund meets with Palestinian female students in Turkey and offers them a number of scholarships.",
    date: "March 18, 2023",
    location: "İstanbul, Türkiye",
    description:"The Palestinian Student Support Fund organized a networking event with Palestinian female students in Turkey, aiming to understand their needs and introduce them to its supportive mission.This initiative comes as part of efforts to strengthen communication and build future cooperation that would support their academic journey.During the event, a number of financial scholarships were awarded to all participating students.",
    full_description:"As part of its efforts to strengthen communication with Palestinian students abroad, the Palestinian Student Support Fund organized, on (…), a special networking event for Palestinian female students in Turkey.The event aimed to closely learn about their academic and living experiences and to listen to their needs. It also included an introduction to the Fund's mission and its goals in supporting the educational journey.The meeting provided a space for open dialogue and exchange of perspectives, in a step aimed at building future bridges of cooperation to further support the students and their academic endeavors.During the event, a number of scholarships and financial aids were distributed to the attending students, reflecting the Fund's commitment to supporting the educational journey of the Palestinian people and ensuring favorable conditions for them to complete their university studies.",
    image: "/two/PHOTO-2025-04-20-18-04-03.jpg?height=400&width=600",
    category: "Cultural Event",
    featured: true,
    year: 2023,
  },
  {
    id: 3,
    title: "The Palestinian Student Support Fund meets with Palestinian students to strengthen support and communication",
    date: "November 5-6, 2022",
    location: "Kuala Lumpur, Malaysia",
    description:"The Palestinian Student Support Fund organized a networking event with Palestinian students in Malaysia, aiming to foster connections, listen to their needs, and introduce them to the Fund's mission and its role in supporting their academic journey, reaffirming its commitment to supporting students wherever they may be.",
    full_description:"As part of its vision to reach Palestinian students wherever they are, the Palestinian Student Support Fund organized a networking event with Palestinian students in Malaysia, aiming to strengthen direct communication, foster connections, and listen to their academic and living aspirations and needs. During the event, representatives of the Fund provided a comprehensive overview of its mission, goals, and its role in supporting Palestinian students and enabling them to pursue higher education in a stable and secure environment. The Fund also emphasized that its support for students knows no geographical boundaries. In addition, scholarships and financial aid were awarded to the Palestinian students who participated in the event.",
    image: "/three/PHOTO-2025-04-20-18-04-18.jpg?height=400&width=600",
    category: "Education Fair",
    featured: true,
    year: 2022,
  },
  {
    id: 4,
    title: "A networking event organized by the Palestinian Student Support Fund with Palestinian students, during which a number of financial scholarships were awarded.",
    date: "August 10-12, 2022",
    location: "Bishkek, Kyrgyzstan",
    description:"The Palestinian Student Support Fund organized a networking event with Palestinian students in Kyrgyzstan to learn about their experiences and needs, introduce them to the Fund's mission, and provide financial scholarships to all attendees.",
    full_description:"As part of its ongoing efforts to reach Palestinian students around the world, the Palestinian Student Support Fund organized a networking event with Palestinian students in Kyrgyzstan.The event aimed to foster connections, listen to the students' experiences and needs, and introduce them to the vital role the Fund plays in supporting their academic journeys and helping them overcome the challenges they face. The gathering also included a fraternal meal, designed to enhance the spirit of friendliness and direct engagement between the students and the Fund's representatives. This initiative contributed to strengthening community bonds and promoting a spirit of cooperation and belonging among Palestinian students abroad. At the conclusion of the event, financial scholarships were distributed to all participating students, reaffirming the Fund's unwavering commitment to supporting Palestinian students wherever they are and its dedication to accompanying them throughout their journey of learning and success, thereby enhancing their opportunities to achieve their academic goals.",
    image: "/four/PHOTO-2025-04-26-22-18-22 2.jpg?height=400&width=600",
    category: "Workshop",
    featured: false,
    year: 2022,
  },
  {
    id: 5,
    title: "The Palestinian Student Support Fund organizes a friendly match with Palestinian students in Turkey, as a recreational initiative to strengthen engagement with the students.",
    date: "April 22-23, 2022",
    location: "İstanbul, Türkiye",
    description:"The Palestinian Student Support Fund organized a friendly football match that brought together a number of Palestinian students in istanbul , Turkey.The event aimed to strengthen bonds and foster friendly interaction, creating a space for networking and communication in an informal atmosphere, while also introducing the Fund's mission and activities.",
    full_description:"As part of its efforts to strengthen engagement with Palestinian students abroad, the Palestinian Student Support Fund organized a friendly football match on (...) in istanbul , bringing together a number of Palestinian students in Turkey amid an atmosphere of energy and interaction.This sports initiative came within the Fund's broader efforts to build direct relationships with students, create a supportive environment that fosters networking and breaks down formal barriers, and to present a lively image of the Fund's activities and its mission of support for students. The match received a positive response from the participants, who expressed their happiness with the initiative, appreciating the opportunity it provided for social bonding and for getting to know the Fund's activities in a friendly and informal setting.",
    image: "/five/DSC07508.jpg?height=400&width=600",
    category: "Seminar",
    featured: false,
    year: 2022,
  },
  {
    id: 6,
    title: "The Palestinian Student Support Fund meets with Palestinian students in Egypt and provides them with financial scholarships.",
    date: "October 15-17, 2021",
    location: "Cairo, Eygpt",
    description: "The Palestinian Student Support Fund organized a networking event with Palestinian students in Egypt, aiming to foster connections and listen to their views and needs.During the event, financial scholarships were distributed to the attendees, who expressed their appreciation for the Fund's role in supporting their educational journey.",
    full_description:"As part of its ongoing efforts to enhance communication with Palestinian students abroad, the Palestinian Student Support Fund organized a networking event in the Arab Republic of Egypt, bringing together a number of Palestinian students to engage directly with each other and learn about their educational and living needs. During the meeting, the Fund provided several financial grants to the students in attendance, supporting their academic journeys and alleviating their living burdens. This initiative was met with positive feedback from those present, who expressed their gratitude and appreciation to the Fund, praising its role in accompanying them throughout their educational journeys and its commitment to reaching Palestinian students wherever they are. The participating students also invited their Palestinian colleagues to learn more about the Fund's mission and benefit from its programs, emphasizing that the Palestinian Student Support Fund constitutes a true pillar of support for students at home and abroad, enhancing their opportunities for academic success.",
    image: "/six/PHOTO-2025-04-26-22-24-15.jpg?height=400&width=600",
    category: "Networking",
    featured: false,
    year: 2021,
  },
  {
    id: 7,
    title: "The Palestinian Student Support Fund in Mauritania Organizes a Meeting with Palestinian Students",
    date: "October 15-17, 2021",
    location: "Nouakchott, mauritania",
    description: "As part of its efforts to reach the largest number of Palestinian students, the Palestinian Student Support Fund organized a networking meeting in Mauritania bringing together Palestinian students with Mauritanian and Palestinian figures. Financial grants were distributed during the meeting, along with an agreement to provide recurring monthly grants to support Palestinian students there.",
    full_description:"As part of expanding its efforts to support Palestinian students around the world, the Palestinian Student Support Fund organized a networking meeting in Mauritania, attended by a number of Palestinian students, along with prominent Mauritanian and Palestinian figures. The meeting provided an important platform for acquaintance, strengthening ties, and exchanging views on ways to support Palestinian students residing in Mauritania. During the meeting, direct financial grants were distributed to a number of students, demonstrating the Fund's commitment to providing practical and sustainable support to students abroad. An agreement was also announced to provide recurring monthly grants to Palestinian students in Mauritania, a qualitative development in the Fund's efforts to enhance the stability of support and expand its scope. The meeting was widely welcomed by attendees, who praised the initiative and considered it a strong boost to students' educational progress and an example of the true partnership between the Palestinian and Mauritanian peoples in supporting education and human dignity.",
    image: "/placeholder.svg",
    category: "Networking",
    featured: false,
    year: 2021,
  }
]

// Categories for filtering
const categories = [
  "All",
  "Conference",
  "Cultural Event",
  "Education Fair",
  "Workshop",
  "Seminar",
  "Networking"
]
const years = ["All", "2023", "2022", "2021"]
const locations = ["All", "Cairo, Egypt", "Bishkek, Kyrgyzstan", "Kuala Lumpur, Malaysia", "Nouakchott, Mauritania"]

export default function ActivitiesPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [selectedYear, setSelectedYear] = useState("All")
  const [selectedLocation, setSelectedLocation] = useState("All")
  const [currentPage, setCurrentPage] = useState(1)
  const [activeTab, setActiveTab] = useState("all")
  const itemsPerPage = 6

  // State for individual activity gallery modals
  const [galleryActivity, setGalleryActivity] = useState<typeof activitiesData[0] | null>(null)
  const [isGalleryOpen, setIsGalleryOpen] = useState(false)

  // State for testimonial video modal
  const [isTestimonialVideoOpen, setIsTestimonialVideoOpen] = useState(false)
  const [testimonialVideoPath, setTestimonialVideoPath] = useState("")
  const [testimonialName, setTestimonialName] = useState("")

  const handleViewGallery = (activity: typeof activitiesData[0]) => {
    setGalleryActivity(activity)
    setIsGalleryOpen(true)
  }

  const handleOpenTestimonialVideo = (name: string, videoFileName: string) => {
    setTestimonialName(name)
    setTestimonialVideoPath(`/testomenialVid/${videoFileName}`)
    setIsTestimonialVideoOpen(true)
  }

  const scrollToActivityArchive = () => {
    // @ts-ignore - we know this exists
    const archiveSection = window.activityArchiveRef;

    if (archiveSection) {
      // Set the active tab to "all" to show all activities
      setActiveTab("all")

      // Scroll to the archive section with GSAP animation
      gsap.to(window, {
        duration: 0.2,
        scrollTo: {
          y: archiveSection,
          offsetY: 40 // Add some offset to account for fixed headers
        },
        ease: "power2.inOut"
      });
    }
  }

  useEffect(() => {
    gsap.registerPlugin(ScrollTrigger, ScrollToPlugin)

    // Initialize progress bar
    const progressBar = document.querySelector(".progress-bar")
    if (progressBar) {
      gsap.to(progressBar, {
        scaleX: 1,
        ease: "none",
        scrollTrigger: {
          trigger: document.body,
          start: "top top",
          end: "bottom bottom",
          scrub: 0.3,
        },
      })
    }

    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill())
    }
  }, [])

  // Filter activities based on search query, category, year, location, and tab
  const filteredActivities = activitiesData.filter((activity) => {
    const matchesSearch =
      activity.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      activity.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      activity.location.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesCategory = selectedCategory === "All" || activity.category === selectedCategory
    const matchesYear = selectedYear === "All" || activity.year.toString() === selectedYear
    const matchesLocation = selectedLocation === "All" || activity.location === selectedLocation

    const matchesTab = activeTab === "all" || (activeTab === "featured" && activity.featured)

    return matchesSearch && matchesCategory && matchesYear && matchesLocation && matchesTab
  })

  // Pagination
  const totalPages = Math.ceil(filteredActivities.length / itemsPerPage)
  const paginatedActivities = filteredActivities.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [searchQuery, selectedCategory, selectedYear, selectedLocation, activeTab])

  // Activity type icons
  const getActivityIcon = (category: string) => {
    switch (category) {
      case "Conference":
        return <Users className="h-5 w-5" />
      case "Workshop":
        return <BookOpen className="h-5 w-5" />
      case "Seminar":
        return <Presentation className="h-5 w-5" />
      case "Cultural Event":
        return <Globe className="h-5 w-5" />
      case "Networking":
        return <Handshake className="h-5 w-5" />
      case "Education Fair":
        return <Award className="h-5 w-5" />
      default:
        return <Calendar className="h-5 w-5" />
    }
  }

  return (
    <main className="flex min-h-screen flex-col">
      {/* Activity Gallery Hero Section */}
      <ActivityGalleryHero
        activities={activitiesData}
        onViewGallery={scrollToActivityArchive}
      />

      {/* Gallery Modal for individual activities */}
      {galleryActivity && (
        <ActivityGalleryModal
          isOpen={isGalleryOpen}
          onClose={() => setIsGalleryOpen(false)}
          activity={galleryActivity}
        />
      )}

      {/* Testimonial Video Modal */}
      <TestimonialVideoModal
        isOpen={isTestimonialVideoOpen}
        onClose={() => setIsTestimonialVideoOpen(false)}
        name={testimonialName}
        videoPath={testimonialVideoPath}
      />

      {/* Featured Activities */}
      <section className="py-16 md:py-24 bg-gradient-to-r from-[hsl(0,76%,40%)]/5 via-transparent to-[hsl(120,61%,34%)]/5 dark:from-[hsl(0,76%,40%)]/10 dark:via-black/80 dark:to-[hsl(120,61%,34%)]/10">
        <div className="container px-4 md:px-6">
          <GSAPReveal animation="slide-up">
            <div className="mb-16 text-center">
              <div className="inline-flex items-center rounded-lg bg-[hsl(120,61%,34%)]/10 px-4 py-2 text-base text-[hsl(120,61%,34%)]">
                <Award className="mr-2 h-5 w-5" />
                Featured Events
              </div>
              <h2 className="mt-3 text-4xl font-bold sm:text-5xl">Highlighted Activities</h2>
              <p className="mx-auto mt-6 max-w-[800px] text-muted-foreground text-xl">
                Our most impactful recent events and initiatives.
              </p>
            </div>
          </GSAPReveal>

          <div className="mx-auto grid max-w-7xl gap-10 md:grid-cols-1 lg:grid-cols-3">
            {activitiesData
              .filter((activity) => activity.featured)
              .slice(0, 3)
              .map((activity, index) => (
                <GSAPReveal key={activity.id} animation="slide-up" delay={0.1 * index}>
                  <Card
                    className="h-full overflow-hidden hover:shadow-xl transition-all duration-300 border-2 border-[hsl(120,61%,34%)]/20 hover:border-[hsl(120,61%,34%)]/60 hover:-translate-y-2 cursor-pointer"
                    onClick={scrollToActivityArchive}
                  >
                    <div className="aspect-video overflow-hidden">
                      <img
                        src={activity.image || "/placeholder.svg"}
                        alt={activity.title}
                        className="h-full w-full object-cover transition-transform duration-500 hover:scale-105"
                      />
                    </div>
                    <CardContent className="p-8">
                      <div className="mb-4 flex items-center justify-between">
                        <Badge variant="outline" className="bg-[hsl(0,76%,40%)]/10 text-[hsl(0,76%,40%)] text-base px-3 py-1">
                          {activity.category}
                        </Badge>
                        <span className="text-base font-medium text-muted-foreground">{activity.year}</span>
                      </div>
                      <h3 className="mb-4 text-2xl font-bold">{activity.title}</h3>
                      <div className="mb-6 flex flex-col space-y-2 text-base text-muted-foreground">
                        <div className="flex items-center">
                          <Calendar className="mr-3 h-5 w-5" />
                          {activity.date}
                        </div>
                        <div className="flex items-center">
                          <MapPin className="mr-3 h-5 w-5" />
                          {activity.location}
                        </div>
                      </div>
                      <p className="line-clamp-4 text-muted-foreground text-lg">{activity.description}</p>
                    </CardContent>
                  </Card>
                </GSAPReveal>
              ))}
          </div>
        </div>
      </section>

      {/* Impact Statistics */}
      <section className="py-16 md:py-24 bg-gradient-to-r from-[hsl(0,76%,40%)] via-black to-[hsl(120,61%,34%)] text-white">
        <div className="container px-4 md:px-6">
          <GSAPReveal animation="slide-up">
            <div className="mb-12 text-center">
              <h2 className="text-3xl font-bold sm:text-4xl">Our Impact</h2>
              <p className="mx-auto mt-4 max-w-[700px] text-white/90">
                The collective reach and influence of our activities and programs.
              </p>
            </div>
          </GSAPReveal>

          <div className="mx-auto grid max-w-4xl gap-8 md:grid-cols-2 lg:grid-cols-4">
            <GSAPReveal animation="fade" delay={0.1}>
              <div className="flex flex-col items-center text-center">
                <span className="text-5xl font-bold">7</span>
                <span className="mt-2 text-lg">Events & Activities</span>
              </div>
            </GSAPReveal>
            <GSAPReveal animation="fade" delay={0.2}>
              <div className="flex flex-col items-center text-center">
                <span className="text-5xl font-bold">1,620+</span>
                <span className="mt-2 text-lg">Participants</span>
              </div>
            </GSAPReveal>
            <GSAPReveal animation="fade" delay={0.3}>
              <div className="flex flex-col items-center text-center">
                <span className="text-5xl font-bold">5</span>
                <span className="mt-2 text-lg">City</span>
              </div>
            </GSAPReveal>
            <GSAPReveal animation="fade" delay={0.4}>
              <div className="flex flex-col items-center text-center">
                <span className="text-5xl font-bold">98%</span>
                <span className="mt-2 text-lg">Satisfaction Rate</span>
              </div>
            </GSAPReveal>
          </div>
        </div>
      </section>

      {/* All Activities */}
      <section
        ref={(el) => {
          if (el) {
            // @ts-ignore - we know this is an HTMLElement
            window.activityArchiveRef = el;
          }
        }}
        className="py-16 md:py-24 bg-gradient-to-r from-[hsl(0,76%,40%)]/5 via-transparent to-[hsl(120,61%,34%)]/5 dark:from-[hsl(0,76%,40%)]/10 dark:via-black/80 dark:to-[hsl(120,61%,34%)]/10">
        <div className="container px-4 md:px-6">
          <GSAPReveal animation="slide-up">
            <div className="mb-12 text-center">
              <div className="inline-flex items-center rounded-lg bg-[hsl(0,76%,40%)]/10 px-3 py-1 text-sm text-[hsl(0,76%,40%)]">
                <Calendar className="mr-1 h-4 w-4" />
                Past Events
              </div>
              <h2 className="mt-2 text-3xl font-bold sm:text-4xl">Activity Archive</h2>
              <p className="mx-auto mt-4 max-w-[700px] text-muted-foreground">
                Browse through our complete history of events, workshops, and initiatives.
              </p>
            </div>
          </GSAPReveal>

          <div className="mx-auto max-w-5xl">
            {/* Search and Filter */}
            <GSAPReveal animation="fade">
              <div className="mb-8 grid gap-4 md:grid-cols-4">
                <div className="relative md:col-span-1">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="Search activities..."
                    className="pl-10"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <div className="flex items-center gap-2">
                  <Filter className="h-4 w-4 text-muted-foreground" />
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger>
                      <SelectValue placeholder="Activity Type" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center gap-2">
                  <Select value={selectedYear} onValueChange={setSelectedYear}>
                    <SelectTrigger>
                      <SelectValue placeholder="Year" />
                    </SelectTrigger>
                    <SelectContent>
                      {years.map((year) => (
                        <SelectItem key={year} value={year}>
                          {year}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center gap-2">
                  <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                    <SelectTrigger>
                      <SelectValue placeholder="Location" />
                    </SelectTrigger>
                    <SelectContent>
                      {locations.map((location) => (
                        <SelectItem key={location} value={location}>
                          {location}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </GSAPReveal>

            {/* Tabs */}
            <GSAPReveal animation="fade" delay={0.1}>
              <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-8">
                <TabsList className="grid w-full max-w-md grid-cols-2 bg-[hsl(0,76%,40%)]/5 dark:bg-[hsl(0,76%,40%)]/10">
                  <TabsTrigger
                    value="all"
                    className="data-[state=active]:bg-[hsl(120,61%,34%)] data-[state=active]:text-white"
                  >
                    All Activities
                  </TabsTrigger>
                  <TabsTrigger
                    value="featured"
                    className="data-[state=active]:bg-[hsl(120,61%,34%)] data-[state=active]:text-white"
                  >
                    Highlighted
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </GSAPReveal>

            {/* Activities Grid */}
            {paginatedActivities.length > 0 ? (
              <div className="space-y-6">
                {paginatedActivities.map((activity, index) => (
                  <GSAPReveal key={activity.id} animation="fade" delay={0.1 * index}>
                    <Card
                      className="overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
                      onClick={() => handleViewGallery(activity)}
                    >
                      <div className="grid md:grid-cols-3">
                        <div className="aspect-video md:aspect-square overflow-hidden">
                          <img
                            src={activity.image || "/placeholder.svg"}
                            alt={activity.title}
                            className="h-full w-full object-cover"
                          />
                        </div>
                        <div className="p-6 md:col-span-2">
                          <div className="mb-3 flex flex-wrap items-center gap-2">
                            <Badge variant="outline" className="bg-[hsl(120,61%,34%)]/10 text-[hsl(120,61%,34%)] flex items-center gap-1">
                              {getActivityIcon(activity.category)}
                              {activity.category}
                            </Badge>
                            <span className="text-sm text-muted-foreground">{activity.year}</span>
                          </div>
                          <h3 className="mb-2 text-xl font-bold">{activity.title}</h3>
                          <div className="mb-4 flex flex-wrap gap-4 text-sm text-muted-foreground">
                            <div className="flex items-center">
                              <Calendar className="mr-2 h-4 w-4" />
                              {activity.date}
                            </div>
                            <div className="flex items-center">
                              <MapPin className="mr-2 h-4 w-4" />
                              {activity.location}
                            </div>
                          </div>
                          <p className="mb-4 text-muted-foreground">{activity.description}</p>
                        </div>
                      </div>
                    </Card>
                  </GSAPReveal>
                ))}
              </div>
            ) : (
              <div className="rounded-lg border border-dashed p-8 text-center">
                <p className="text-muted-foreground">No activities found matching your criteria.</p>
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <GSAPReveal animation="fade" delay={0.2}>
                <div className="mt-8 flex items-center justify-center gap-2">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  {Array.from({ length: totalPages }).map((_, index) => (
                    <Button
                      key={index}
                      variant={currentPage === index + 1 ? "default" : "outline"}
                      className={currentPage === index + 1 ? "bg-[hsl(120,61%,34%)] text-white hover:bg-[hsl(120,61%,34%)]/90" : ""}
                      size="icon"
                      onClick={() => setCurrentPage(index + 1)}
                    >
                      {index + 1}
                    </Button>
                  ))}
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </GSAPReveal>
            )}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16 md:py-24 bg-gradient-to-r from-[hsl(0,76%,40%)]/5 via-transparent to-[hsl(120,61%,34%)]/5 dark:from-[hsl(0,76%,40%)]/10 dark:via-black/80 dark:to-[hsl(120,61%,34%)]/10">
        <div className="container px-4 md:px-6">
          <GSAPReveal animation="slide-up">
            <div className="mb-12 text-center">
              <div className="inline-flex items-center rounded-lg bg-[hsl(0,76%,40%)]/10 px-3 py-1 text-sm text-[hsl(0,76%,40%)]">
                <Quote className="mr-1 h-4 w-4" />
                Student Voices
              </div>
              <h2 className="mt-2 text-3xl font-bold sm:text-4xl">Participant Testimonials</h2>
              <p className="mx-auto mt-4 max-w-[700px] text-muted-foreground">
                Hear from students who have attended our events and activities.
              </p>
            </div>
          </GSAPReveal>

          <div className="mx-auto grid max-w-5xl gap-8 md:grid-cols-2 lg:grid-cols-3">
            <GSAPReveal animation="slide-up" delay={0.1}>
              <Card
                className="h-full cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => handleOpenTestimonialVideo("Tasnim mosa", "moritania.mp4")}
              >
                <CardContent className="flex h-full flex-col justify-between p-6">
                  <div>
                    <Quote className="mb-4 h-8 w-8 text-[hsl(120,61%,34%)]/40" />
                    <p className="mb-4 italic text-muted-foreground">
                      "The Annual Scholarship Conference was an incredible opportunity to connect with other students and learn about educational opportunities. I gained valuable insights that will help me in my academic journey."
                    </p>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Tasnim mosa</p>
                      <p className="text-sm text-muted-foreground">Master's Student, Environmental Engineering</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </GSAPReveal>

            <GSAPReveal animation="slide-up" delay={0.2}>
              <Card
                className="h-full cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => handleOpenTestimonialVideo("Hassan Albirok", "hassan.mp4")}
              >
                <CardContent className="flex h-full flex-col justify-between p-6">
                  <div>
                    <Quote className="mb-4 h-8 w-8 text-[hsl(0,76%,40%)]/40" />
                    <p className="mb-4 italic text-muted-foreground">
                      "The Cultural Exchange Festival was a highlight of my year. It gave me a chance to share my heritage with the local community and feel connected to our Palestinian roots."
                    </p>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Hassan Albirok</p>
                      <p className="text-sm text-muted-foreground">Bachelor's Student, Mechanical Engineering</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </GSAPReveal>

            <GSAPReveal animation="slide-up" delay={0.3}>
              <Card
                className="h-full cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => handleOpenTestimonialVideo("Atia Algadi", "atia.mp4")}
              >
                <CardContent className="flex h-full flex-col justify-between p-6">
                  <div>
                    <Quote className="mb-4 h-8 w-8 text-[hsl(120,61%,34%)]/40" />
                    <p className="mb-4 italic text-muted-foreground">
                      "The International Education Fair opened doors for me that I never thought possible. I was able to connect with universities from around the world and find scholarship opportunities that matched my academic goals."
                    </p>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Atia Algadi</p>
                      <p className="text-sm text-muted-foreground">PhD Student, Biochemistry</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </GSAPReveal>
            <GSAPReveal animation="slide-up" delay={0.4}>
              <Card
                className="h-full cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => handleOpenTestimonialVideo("Omer Qadih", "omer.mp4")}
              >
                <CardContent className="flex h-full flex-col justify-between p-6">
                  <div>
                    <Quote className="mb-4 h-8 w-8 text-[hsl(120,61%,34%)]/40" />
                    <p className="mb-4 italic text-muted-foreground">
                      "The International Education Fair opened doors for me that I never thought possible. I was able to connect with universities from around the world and find scholarship opportunities that matched my academic goals."
                    </p>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Omer Qadih</p>
                      <p className="text-sm text-muted-foreground">PhD Student, Biochemistry</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </GSAPReveal>
            <GSAPReveal animation="slide-up" delay={0.5}>
              <Card
                className="h-full cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => handleOpenTestimonialVideo("Mohammed Algodra", "mohammed.mp4")}
              >
                <CardContent className="flex h-full flex-col justify-between p-6">
                  <div>
                    <Quote className="mb-4 h-8 w-8 text-[hsl(120,61%,34%)]/40" />
                    <p className="mb-4 italic text-muted-foreground">
                      "The International Education Fair opened doors for me that I never thought possible. I was able to connect with universities from around the world and find scholarship opportunities that matched my academic goals."
                    </p>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Mohammed Algodra</p>
                      <p className="text-sm text-muted-foreground">PhD Student, Biochemistry</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </GSAPReveal>
          </div>
        </div>
      </section>

      {/* Upcoming Events CTA */}
      <section className="py-16 md:py-24 bg-gradient-to-r from-[hsl(0,76%,40%)]/5 via-transparent to-[hsl(120,61%,34%)]/5 dark:from-[hsl(0,76%,40%)]/10 dark:via-black/80 dark:to-[hsl(120,61%,34%)]/10">
        <div className="container px-4 md:px-6">
          <div className="mx-auto max-w-3xl rounded-lg bg-gradient-to-r from-[hsl(0,76%,40%)]/10 via-transparent to-[hsl(120,61%,34%)]/10 dark:from-[hsl(0,76%,40%)]/20 dark:via-black/80 dark:to-[hsl(120,61%,34%)]/20 p-8 text-center">
            <GSAPReveal animation="slide-up">
              <div className="inline-flex items-center rounded-lg bg-[hsl(120,61%,34%)]/10 px-3 py-1 text-sm text-[hsl(120,61%,34%)]">
                <Calendar className="mr-1 h-4 w-4" />
                Stay Connected
              </div>
              <h2 className="mt-2 text-2xl font-bold sm:text-3xl">Join Our Upcoming Events</h2>
              <p className="mx-auto mt-4 max-w-[700px] text-muted-foreground">
                Stay connected with our community and participate in our upcoming workshops, seminars, and networking
                opportunities.
              </p>
              <div className="mt-6">
                <Link href="/contact">
                  <Button className="bg-[hsl(120,61%,34%)] text-white hover:bg-[hsl(120,61%,34%)]/90 dark:bg-[hsl(120,61%,34%)] dark:text-white dark:hover:bg-[hsl(120,61%,34%)]/90">Subscribe to Event Updates</Button>
                </Link>
              </div>
            </GSAPReveal>
          </div>
        </div>
      </section>
    </main>
  )
}
