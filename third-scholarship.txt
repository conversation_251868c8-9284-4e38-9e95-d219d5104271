                  <GSAPReveal animation="slide-up" delay={0.3} className="h-full">
                    <div className="group relative h-full overflow-hidden rounded-xl transition-all duration-500 hover:shadow-2xl">
                      {/* Background gradient with animation */}
                      <div className="absolute inset-0 bg-gradient-to-tr from-black via-[hsl(0,76%,40%)] to-black opacity-90 transition-all duration-700 group-hover:opacity-100"></div>

                      {/* Animated pattern overlay */}
                      <div className="absolute inset-0 bg-[url('/pattern.svg')] bg-repeat opacity-10 mix-blend-overlay transition-all duration-700 group-hover:opacity-20"></div>

                      {/* Content container */}
                      <div className="relative z-10 p-8 text-white h-full flex flex-col">
                        {/* Header with icon */}
                        <div className="flex items-center gap-4 mb-6">
                          <div className="flex h-16 w-16 items-center justify-center rounded-full bg-white/10 backdrop-blur-sm transition-all duration-500 group-hover:scale-110 group-hover:bg-white/20 card-icon">
                            <BookOpen className="h-8 w-8 text-white transition-all duration-500 group-hover:text-white" />
                          </div>
                          <div>
                            <h3 className="text-2xl font-bold font-sora tracking-tight">Research Excellence Scholarship</h3>
                            <p className="text-white/80 text-sm">Advanced Research Support</p>
                          </div>
                        </div>

                        {/* Main description */}
                        <div className="mb-4 border-l-4 border-white pl-4 py-2">
                          <p className="text-white/90">
                            Supporting Palestinian researchers and academics pursuing advanced degrees with a focus on innovative research that addresses critical challenges facing Palestinian communities.
                          </p>
                          <div className="mt-4 space-y-3">
                            <h4 className="text-white font-medium text-sm uppercase tracking-wider">Scholarship Goals</h4>
                            <div className="grid grid-cols-1 gap-3">
                              <div className="flex items-start gap-3 bg-white/5 p-3 rounded-lg backdrop-blur-sm transition-all duration-300 hover:bg-white/10 hover:translate-x-1 hover:shadow-lg">
                                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-white/20">
                                  <Lightbulb className="h-4 w-4 text-white" />
                                </div>
                                <div>
                                  <p className="text-white/90 text-sm">Advancing innovative research that addresses critical challenges in Palestinian society</p>
                                </div>
                              </div>

                              <div className="flex items-start gap-3 bg-white/5 p-3 rounded-lg backdrop-blur-sm transition-all duration-300 hover:bg-white/10 hover:translate-x-1 hover:shadow-lg">
                                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-white/20">
                                  <Globe className="h-4 w-4 text-white" />
                                </div>
                                <div>
                                  <p className="text-white/90 text-sm">Building research capacity and academic excellence in Palestinian institutions</p>
                                </div>
                              </div>

                              <div className="flex items-start gap-3 bg-white/5 p-3 rounded-lg backdrop-blur-sm transition-all duration-300 hover:bg-white/10 hover:translate-x-1 hover:shadow-lg">
                                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-white/20">
                                  <Users className="h-4 w-4 text-white" />
                                </div>
                                <div>
                                  <p className="text-white/90 text-sm">Supporting knowledge transfer and international academic collaboration</p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Details grid */}
                        <div className="grid grid-cols-2 gap-6 mb-8">
                          <div className="flex flex-col gap-2 bg-white/5 p-4 rounded-lg backdrop-blur-sm transition-all duration-300 hover:bg-white/10">
                            <div className="flex items-center gap-2">
                              <Calendar className="h-5 w-5 text-white" />
                              <span className="font-medium">Duration</span>
                            </div>
                            <p className="text-white/80 text-sm">2-3 years (PhD research)</p>
                          </div>

                          <div className="flex flex-col gap-2 bg-white/5 p-4 rounded-lg backdrop-blur-sm transition-all duration-300 hover:bg-white/10">
                            <div className="flex items-center gap-2">
                              <Clock className="h-5 w-5 text-white" />
                              <span className="font-medium">Deadline</span>
                            </div>
                            <p className="text-white/80 text-sm">January 15 (annual)</p>
                          </div>

                          <div className="flex flex-col gap-2 bg-white/5 p-4 rounded-lg backdrop-blur-sm transition-all duration-300 hover:bg-white/10">
                            <div className="flex items-center gap-2">
                              <CheckSquare className="h-5 w-5 text-white" />
                              <span className="font-medium">Requirements</span>
                            </div>
                            <ul className="mt-2 space-y-1">
                              <li className="flex items-center gap-2">
                                <div className="h-2 w-2 rounded-full bg-white"></div>
                                <span className="text-white/80 text-sm">Master's degree with distinction</span>
                              </li>
                              <li className="flex items-center gap-2">
                                <div className="h-2 w-2 rounded-full bg-white"></div>
                                <span className="text-white/80 text-sm">Research proposal</span>
                              </li>
                              <li className="flex items-center gap-2">
                                <div className="h-2 w-2 rounded-full bg-white"></div>
                                <span className="text-white/80 text-sm">Academic references</span>
                              </li>
                              <li className="flex items-center gap-2">
                                <div className="h-2 w-2 rounded-full bg-white"></div>
                                <span className="text-white/80 text-sm">Publication record</span>
                              </li>
                            </ul>
                          </div>

                          <div className="flex flex-col gap-2 bg-white/5 p-4 rounded-lg backdrop-blur-sm transition-all duration-300 hover:bg-white/10">
                            <div className="flex items-center gap-2">
                              <Target className="h-5 w-5 text-white" />
                              <span className="font-medium">Fields of Study</span>
                            </div>
                            <ul className="mt-2 space-y-1">
                              <li className="flex items-center gap-2">
                                <div className="h-2 w-2 rounded-full bg-white"></div>
                                <span className="text-white/80 text-sm">Public Health</span>
                              </li>
                              <li className="flex items-center gap-2">
                                <div className="h-2 w-2 rounded-full bg-white"></div>
                                <span className="text-white/80 text-sm">Sustainable Development</span>
                              </li>
                              <li className="flex items-center gap-2">
                                <div className="h-2 w-2 rounded-full bg-white"></div>
                                <span className="text-white/80 text-sm">Technology & Innovation</span>
                              </li>
                              <li className="flex items-center gap-2">
                                <div className="h-2 w-2 rounded-full bg-white"></div>
                                <span className="text-white/80 text-sm">Social Sciences</span>
                              </li>
                            </ul>
                          </div>
                        </div>

                        {/* Action buttons */}
                        <div className="mt-auto grid grid-cols-2 gap-4">
                          <Link href="/apply" className="group/btn">
                            <Button className="w-full bg-white text-black hover:bg-[hsl(0,76%,40%)] hover:text-white transition-all duration-300 group-hover/btn:shadow-lg">
                              Apply Now
                              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
                            </Button>
                          </Link>
                          <Link href="/donate" className="group/btn">
                            <Button className="w-full bg-[hsl(0,76%,40%)] text-white hover:bg-white hover:text-[hsl(0,76%,40%)] transition-all duration-300 group-hover/btn:shadow-lg">
                              Donate Now
                              <DollarSign className="ml-2 h-4 w-4 transition-all group-hover/btn:scale-110" />
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </div>
                  </GSAPReveal>
